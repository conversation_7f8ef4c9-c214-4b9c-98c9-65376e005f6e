#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"

// MetaHuman DNA Calibration includes
#ifdef WITH_METAHUMAN_DNA_CALIBRATION
#include "DNACalibDNAReader.h"
#include "dnacalib/Command.h"
#include "dnacalib/CommandSequence.h"
#include "dnacalib/commands/SetNeutralJointTranslationsCommand.h"
#include "dnacalib/commands/SetNeutralJointRotationsCommand.h"
#include "dnacalib/commands/SetVertexPositionsCommand.h"
#include "dnacalib/commands/SetBlendShapeTargetDeltasCommand.h"
#include "dnacalib/commands/SetSkinWeightsCommand.h"
#include "dnacalib/commands/SetJointHierarchyCommand.h"
#include "dnacalib/commands/SetAnimatedMapCommand.h"
#include "dnacalib/commands/SetControlCommand.h"
#include "dnacalib/commands/SetMLControlCommand.h"
#include "dnacalib/commands/SetNeuralNetworkCommand.h"
#include "dnacalib/types/Aliases.h"
#endif

// Forward declarations
class FAuracronDNAReader;
enum class EDNACalibrationOperation : uint8;

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronDNACalib, Log, All);

/**
 * Thread-safe wrapper for MetaHuman DNA calibration operations
 * Provides type-safe abstraction over DNACalibDNAReader for editing operations
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronDNACalib
{
public:
    FAuracronDNACalib();
    ~FAuracronDNACalib();

    // Core calibration operations
    bool InitializeFromReader(const FAuracronDNAReader& Reader);
    bool IsValid() const;
    void Reset();

    // Vertex manipulation
    bool SetVertexPositions(int32 MeshIndex, const TArray<FVector>& Positions);
    bool SetVertexPositions(int32 MeshIndex, const TArray<int32>& VertexIndices, const TArray<FVector>& Positions);
    bool TransformVertices(int32 MeshIndex, const TArray<int32>& VertexIndices, const FTransform& Transform);
    bool ScaleVertices(int32 MeshIndex, const TArray<int32>& VertexIndices, const FVector& Scale);

    // Blend shape manipulation
    bool SetBlendShapeTargetDeltas(int32 MeshIndex, int32 TargetIndex, const TArray<FVector>& Deltas);
    bool SetBlendShapeTargetVertexIndices(int32 MeshIndex, int32 TargetIndex, const TArray<int32>& VertexIndices);
    bool AddBlendShapeTarget(int32 MeshIndex, const FString& BlendShapeName, const TArray<int32>& VertexIndices, const TArray<FVector>& Deltas);
    bool RemoveBlendShapeTarget(int32 MeshIndex, int32 TargetIndex);
    bool ScaleBlendShapeDeltas(int32 MeshIndex, int32 TargetIndex, float ScaleFactor);

    // Joint manipulation
    bool SetNeutralJointTranslations(const TArray<FVector>& Translations);
    bool SetNeutralJointRotations(const TArray<FRotator>& Rotations);
    bool SetJointHierarchy(const TArray<int32>& ParentIndices);
    bool SetJointTransform(int32 JointIndex, const FTransform& Transform);
    bool ScaleJoint(int32 JointIndex, const FVector& Scale);

    // Skin weights manipulation
    bool SetSkinWeights(int32 MeshIndex, int32 VertexIndex, const TArray<float>& Weights, const TArray<int32>& JointIndices);
    bool NormalizeSkinWeights(int32 MeshIndex);
    bool PruneSkinWeights(int32 MeshIndex, float WeightThreshold = 0.001f);

    // Animation data manipulation
    bool SetAnimatedMapValues(int32 MapIndex, const TArray<float>& Values);
    bool AddAnimatedMap(const FString& MapName, const TArray<float>& Values);
    bool RemoveAnimatedMap(int32 MapIndex);
    bool ScaleAnimatedMapValues(int32 MapIndex, float ScaleFactor);

    // Control rig manipulation
    bool SetControlValues(int32 ControlIndex, const TArray<float>& Values);
    bool AddControl(const FString& ControlName, const TArray<float>& Values);
    bool RemoveControl(int32 ControlIndex);
    bool SetControlLimits(int32 ControlIndex, const TArray<float>& MinValues, const TArray<float>& MaxValues);

    // Machine learning data manipulation
    bool SetMLControlValues(int32 ControlIndex, const TArray<float>& Values);
    bool AddMLControl(const FString& ControlName, const TArray<float>& Values);
    bool RemoveMLControl(int32 ControlIndex);
    bool TrainMLControl(int32 ControlIndex, const TArray<TArray<float>>& TrainingData);

    // Neural network manipulation
    bool SetNeuralNetworkWeights(int32 NetworkIndex, const TArray<float>& Weights);
    bool SetNeuralNetworkBiases(int32 NetworkIndex, const TArray<float>& Biases);
    bool AddNeuralNetwork(const FString& NetworkName, const TArray<int32>& Topology, const TArray<float>& Weights, const TArray<float>& Biases);
    bool RemoveNeuralNetwork(int32 NetworkIndex);
    bool TrainNeuralNetwork(int32 NetworkIndex, const TArray<TArray<float>>& InputData, const TArray<TArray<float>>& OutputData);

    // Batch operations
    bool BeginBatchOperation();
    bool EndBatchOperation();
    bool ExecuteBatchOperation();
    bool CancelBatchOperation();
    bool AddToBatch(EDNACalibrationOperation Operation, const TArray<FString>& Parameters);

    // Validation and optimization
    bool ValidateCalibration() const;
    bool OptimizeForPerformance();
    bool OptimizeForSize();
    bool RepairCorruption();

    // Undo/Redo system
    bool CanUndo() const;
    bool CanRedo() const;
    bool Undo();
    bool Redo();
    void ClearUndoHistory();

    // Export operations
    bool ExportToReader(FAuracronDNAReader& OutReader) const;
    bool SaveToFile(const FString& FilePath, bool bBinaryFormat = true) const;

    // Error handling
    FString GetLastError() const;
    TArray<FString> GetValidationErrors() const;
    TArray<FString> GetValidationWarnings() const;

    // Thread safety
    mutable FCriticalSection AccessMutex;

private:
    // Native DNA calibration instance
#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    TUniquePtr<dnacalib::DNACalibDNAReader> NativeCalib;
    TUniquePtr<dnacalib::CommandSequence> BatchCommands;
#else
    void* NativeCalib;
    void* BatchCommands;
#endif

    // State tracking
    FThreadSafeBool bIsValid;
    FThreadSafeBool bInBatchOperation;
    FString LastError;
    TArray<FString> ValidationErrors;
    TArray<FString> ValidationWarnings;

    // Undo/Redo system
    struct FCalibrationState
    {
        TArray<uint8> SerializedData;
        FString Description;
        FDateTime Timestamp;
    };
    TArray<FCalibrationState> UndoStack;
    TArray<FCalibrationState> RedoStack;
    int32 MaxUndoStates;

    // Internal helper methods
    bool ValidateMeshIndex(int32 MeshIndex) const;
    bool ValidateJointIndex(int32 JointIndex) const;
    bool ValidateBlendShapeTarget(int32 MeshIndex, int32 TargetIndex) const;
    bool ValidateAnimatedMapIndex(int32 MapIndex) const;
    bool ValidateControlIndex(int32 ControlIndex) const;
    bool ValidateMLControlIndex(int32 ControlIndex) const;
    bool ValidateNeuralNetworkIndex(int32 NetworkIndex) const;

    void LogError(const FString& ErrorMessage) const;
    void LogWarning(const FString& WarningMessage) const;
    void SaveUndoState(const FString& Description);
    bool RestoreState(const FCalibrationState& State);

    // Prevent copying
    FAuracronDNACalib(const FAuracronDNACalib&) = delete;
    FAuracronDNACalib& operator=(const FAuracronDNACalib&) = delete;
};
