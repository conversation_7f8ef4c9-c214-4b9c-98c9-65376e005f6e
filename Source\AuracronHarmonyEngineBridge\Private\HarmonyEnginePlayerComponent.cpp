#include "HarmonyEnginePlayerComponent.h"
#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyEngineSubsystem.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"

UHarmonyEnginePlayerComponent::UHarmonyEnginePlayerComponent(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 5.0f; // Tick every 5 seconds
    
    // Enable networking
    SetIsReplicatedByDefault(true);
    
    // Default configuration
    bEnableAutomaticReporting = true;
    BehaviorUpdateInterval = 30.0f;
    bEnableEmotionalTracking = true;
    bEnableRewardNotifications = true;
    
    // Initialize replicated properties
    CurrentToxicityScore = 0.0f;
    CurrentPositivityScore = 0.0f;
    CurrentEmotionalState = EEmotionalState::Neutral;
    TotalKindnessPoints = 0;
    bIsUnderIntervention = false;
    bIsInHealingSession = false;
    
    // Initialize runtime data
    ActionsThisSession = 0;
    LastBehaviorUpdate = FDateTime::Now();
}

void UHarmonyEnginePlayerComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    DOREPLIFETIME(UHarmonyEnginePlayerComponent, CurrentToxicityScore);
    DOREPLIFETIME(UHarmonyEnginePlayerComponent, CurrentPositivityScore);
    DOREPLIFETIME(UHarmonyEnginePlayerComponent, CurrentEmotionalState);
    DOREPLIFETIME(UHarmonyEnginePlayerComponent, TotalKindnessPoints);
    DOREPLIFETIME(UHarmonyEnginePlayerComponent, bIsUnderIntervention);
    DOREPLIFETIME(UHarmonyEnginePlayerComponent, bIsInHealingSession);
}

void UHarmonyEnginePlayerComponent::BeginPlay()
{
    Super::BeginPlay();
    
    // Initialize Harmony Engine integration
    InitializeHarmonyEngine();
    
    // Validate configuration
    ValidateConfiguration();
    
    // Setup timers
    if (UWorld* World = GetWorld())
    {
        if (bEnableAutomaticReporting)
        {
            World->GetTimerManager().SetTimer(
                BehaviorUpdateTimer,
                this,
                &UHarmonyEnginePlayerComponent::UpdateBehaviorMetrics,
                BehaviorUpdateInterval,
                true
            );
        }
        
        if (bEnableEmotionalTracking)
        {
            World->GetTimerManager().SetTimer(
                EmotionalCheckTimer,
                this,
                &UHarmonyEnginePlayerComponent::AnalyzeRecentBehavior,
                60.0f, // Check emotions every minute
                true
            );
        }
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("HarmonyEnginePlayerComponent initialized for player: %s"), *PlayerID);
}

void UHarmonyEnginePlayerComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Unregister from Harmony Engine
    UnregisterFromHarmonyEngine();
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(BehaviorUpdateTimer);
        World->GetTimerManager().ClearTimer(EmotionalCheckTimer);
    }
    
    // Cleanup data
    RecentActions.Empty();
    RecentChatMessages.Empty();
    
    Super::EndPlay(EndPlayReason);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("HarmonyEnginePlayerComponent ended for player: %s"), *PlayerID);
}

void UHarmonyEnginePlayerComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    // Periodic cleanup of old data
    CleanupOldData();
    
    // Update session metrics
    ActionsThisSession++;
}

void UHarmonyEnginePlayerComponent::OnRegister()
{
    Super::OnRegister();
    
    // Get player ID from owner
    if (APlayerController* PC = GetOwningPlayerController())
    {
        if (PC->GetPlayerState<APlayerState>())
        {
            PlayerID = PC->GetPlayerState<APlayerState>()->GetUniqueId().ToString();
        }
    }
}

void UHarmonyEnginePlayerComponent::OnUnregister()
{
    // Unregister from Harmony Engine
    UnregisterFromHarmonyEngine();
    
    Super::OnUnregister();
}

void UHarmonyEnginePlayerComponent::ReportPlayerAction(const FString& ActionType, bool bIsPositive, float Intensity)
{
    if (!bEnableAutomaticReporting || ActionType.IsEmpty())
    {
        return;
    }
    
    // Add to recent actions
    RecentActions.Add(FString::Printf(TEXT("%s:%s:%.2f"), *ActionType, bIsPositive ? TEXT("Pos") : TEXT("Neg"), Intensity));
    
    // Limit recent actions array size
    if (RecentActions.Num() > 50)
    {
        RecentActions.RemoveAt(0, 10); // Remove oldest 10 actions
    }
    
    // Report to server if this is a client
    if (!GetOwningPlayerController()->HasAuthority())
    {
        ServerReportPlayerAction(ActionType, bIsPositive, Intensity);
    }
    else
    {
        // Process on server
        ProcessPlayerActionOnServer(ActionType, bIsPositive, Intensity);
    }
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Reported action for player %s: %s"), *PlayerID, *ActionType);
}

void UHarmonyEnginePlayerComponent::ReportChatMessage(const FString& Message)
{
    if (!bEnableAutomaticReporting || Message.IsEmpty())
    {
        return;
    }
    
    // Add to recent messages
    RecentChatMessages.Add(Message);
    
    // Limit recent messages array size
    if (RecentChatMessages.Num() > 20)
    {
        RecentChatMessages.RemoveAt(0, 5); // Remove oldest 5 messages
    }
    
    // Report to server if this is a client
    if (!GetOwningPlayerController()->HasAuthority())
    {
        ServerReportChatMessage(Message);
    }
    else
    {
        // Process on server
        ProcessChatMessageOnServer(Message);
    }
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Reported chat message for player %s"), *PlayerID);
}

void UHarmonyEnginePlayerComponent::ReportEmotionalState(EEmotionalState NewEmotionalState)
{
    if (!bEnableEmotionalTracking)
    {
        return;
    }
    
    // Update local emotional state
    CurrentEmotionalState = NewEmotionalState;
    
    // Report to server if this is a client
    if (!GetOwningPlayerController()->HasAuthority())
    {
        ServerReportEmotionalState(NewEmotionalState);
    }
    else
    {
        // Process on server
        ProcessEmotionalStateOnServer(NewEmotionalState);
    }
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Reported emotional state for player %s: %d"), 
        *PlayerID, static_cast<int32>(NewEmotionalState));
}

float UHarmonyEnginePlayerComponent::GetCurrentToxicityScore() const
{
    return CurrentToxicityScore;
}

float UHarmonyEnginePlayerComponent::GetCurrentPositivityScore() const
{
    return CurrentPositivityScore;
}

EEmotionalState UHarmonyEnginePlayerComponent::GetCurrentEmotionalState() const
{
    return CurrentEmotionalState;
}

int32 UHarmonyEnginePlayerComponent::GetKindnessPoints() const
{
    return TotalKindnessPoints;
}

bool UHarmonyEnginePlayerComponent::IsUnderIntervention() const
{
    return bIsUnderIntervention;
}

bool UHarmonyEnginePlayerComponent::IsInHealingSession() const
{
    return bIsInHealingSession;
}

void UHarmonyEnginePlayerComponent::RespondToIntervention(const FString& InterventionID, bool bAccepted)
{
    if (!HarmonyEngineSubsystem || InterventionID.IsEmpty())
    {
        return;
    }
    
    // Respond to intervention through Harmony Engine
    EInterventionResult Result = bAccepted ? EInterventionResult::Accepted : EInterventionResult::Declined;
    
    // This would need to be implemented in the intervention system
    UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s responded to intervention %s: %s"), 
        *PlayerID, *InterventionID, bAccepted ? TEXT("Accepted") : TEXT("Declined"));
}

void UHarmonyEnginePlayerComponent::RequestCommunitySupport(const FString& Reason)
{
    if (!HarmonyEngineSubsystem)
    {
        return;
    }
    
    // Request community healing session
    HarmonyEngineSubsystem->InitiateCommunityHealing(PlayerID, TEXT(""));
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s requested community support: %s"), *PlayerID, *Reason);
}

TArray<FHarmonyReward> UHarmonyEnginePlayerComponent::GetAvailableRewards()
{
    if (!HarmonyEngineSubsystem)
    {
        return TArray<FHarmonyReward>();
    }
    
    // Get available rewards from rewards system
    // This would need to be implemented in HarmonyEngineSubsystem
    return TArray<FHarmonyReward>();
}

// Private implementation functions

void UHarmonyEnginePlayerComponent::InitializeHarmonyEngine()
{
    if (UGameInstance* GameInstance = GetWorld()->GetGameInstance())
    {
        HarmonyEngineSubsystem = GameInstance->GetSubsystem<UHarmonyEngineSubsystem>();
        if (HarmonyEngineSubsystem)
        {
            RegisterWithHarmonyEngine();
        }
        else
        {
            UE_LOG(LogHarmonyEngine, Error, TEXT("Failed to get HarmonyEngineSubsystem"));
        }
    }
}

void UHarmonyEnginePlayerComponent::RegisterWithHarmonyEngine()
{
    if (!HarmonyEngineSubsystem)
    {
        return;
    }
    
    // Register player with Harmony Engine
    APlayerController* PC = GetOwningPlayerController();
    if (PC)
    {
        HarmonyEngineSubsystem->RegisterPlayer(PC);
        
        // Bind to events
        HarmonyEngineSubsystem->OnBehaviorDetected.AddDynamic(this, &UHarmonyEnginePlayerComponent::OnBehaviorDetectedInternal);
        HarmonyEngineSubsystem->OnInterventionTriggered.AddDynamic(this, &UHarmonyEnginePlayerComponent::OnInterventionTriggeredInternal);
        HarmonyEngineSubsystem->OnKindnessReward.AddDynamic(this, &UHarmonyEnginePlayerComponent::OnKindnessRewardInternal);
    }
}

void UHarmonyEnginePlayerComponent::UnregisterFromHarmonyEngine()
{
    if (!HarmonyEngineSubsystem)
    {
        return;
    }
    
    // Unbind from events
    HarmonyEngineSubsystem->OnBehaviorDetected.RemoveDynamic(this, &UHarmonyEnginePlayerComponent::OnBehaviorDetectedInternal);
    HarmonyEngineSubsystem->OnInterventionTriggered.RemoveDynamic(this, &UHarmonyEnginePlayerComponent::OnInterventionTriggeredInternal);
    HarmonyEngineSubsystem->OnKindnessReward.RemoveDynamic(this, &UHarmonyEnginePlayerComponent::OnKindnessRewardInternal);
    
    // Unregister player
    APlayerController* PC = GetOwningPlayerController();
    if (PC)
    {
        HarmonyEngineSubsystem->UnregisterPlayer(PC);
    }
}

void UHarmonyEnginePlayerComponent::UpdateBehaviorMetrics()
{
    if (!HarmonyEngineSubsystem || PlayerID.IsEmpty())
    {
        return;
    }
    
    // Create behavior snapshot from recent activity
    FPlayerBehaviorSnapshot BehaviorData;
    BehaviorData.PlayerID = PlayerID;
    BehaviorData.Timestamp = FDateTime::Now();
    BehaviorData.SessionDuration = (FDateTime::Now() - LastBehaviorUpdate).GetTotalSeconds();
    
    // Analyze recent actions
    ProcessActionHistory();
    
    // Calculate behavior scores based on recent activity
    BehaviorData.PositiveActionsCount = CountPositiveActions();
    BehaviorData.NegativeActionsCount = CountNegativeActions();
    BehaviorData.ToxicityScore = CalculateToxicityFromActions();
    BehaviorData.PositivityScore = CalculatePositivityFromActions();
    BehaviorData.EmotionalState = CurrentEmotionalState;
    
    // Update Harmony Engine
    HarmonyEngineSubsystem->UpdatePlayerBehavior(PlayerID, BehaviorData);
    
    LastBehaviorUpdate = FDateTime::Now();
}

// Network function implementations

void UHarmonyEnginePlayerComponent::ServerReportPlayerAction_Implementation(const FString& ActionType, bool bIsPositive, float Intensity)
{
    ProcessPlayerActionOnServer(ActionType, bIsPositive, Intensity);
}

void UHarmonyEnginePlayerComponent::ServerReportChatMessage_Implementation(const FString& Message)
{
    ProcessChatMessageOnServer(Message);
}

void UHarmonyEnginePlayerComponent::ServerReportEmotionalState_Implementation(EEmotionalState NewEmotionalState)
{
    ProcessEmotionalStateOnServer(NewEmotionalState);
}

void UHarmonyEnginePlayerComponent::ClientUpdateHarmonyData_Implementation(float ToxicityScore, float PositivityScore, EEmotionalState EmotionalState, int32 KindnessPoints)
{
    CurrentToxicityScore = ToxicityScore;
    CurrentPositivityScore = PositivityScore;
    CurrentEmotionalState = EmotionalState;
    TotalKindnessPoints = KindnessPoints;
}

void UHarmonyEnginePlayerComponent::ClientShowIntervention_Implementation(const FString& InterventionMessage, EInterventionType InterventionType)
{
    bIsUnderIntervention = true;
    
    // Show intervention UI to player
    UE_LOG(LogHarmonyEngine, Log, TEXT("Showing intervention to player %s: %s"), *PlayerID, *InterventionMessage);
    
    // In a full implementation, this would trigger UI display
    // Example: ShowInterventionWidget(InterventionMessage, InterventionType);
}

void UHarmonyEnginePlayerComponent::ClientShowRewardNotification_Implementation(const FKindnessReward& Reward)
{
    if (!bEnableRewardNotifications)
    {
        return;
    }
    
    // Show reward notification to player
    UE_LOG(LogHarmonyEngine, Log, TEXT("Showing reward notification to player %s: %d points"), 
        *PlayerID, Reward.KindnessPoints);
    
    // In a full implementation, this would trigger reward UI
    // Example: ShowRewardNotificationWidget(Reward);
}

// Event handler implementations

void UHarmonyEnginePlayerComponent::OnBehaviorDetectedInternal(const FString& DetectedPlayerID, const FPlayerBehaviorSnapshot& BehaviorData)
{
    if (DetectedPlayerID != PlayerID)
    {
        return; // Not for this player
    }
    
    // Update local data
    CurrentToxicityScore = BehaviorData.ToxicityScore;
    CurrentPositivityScore = BehaviorData.PositivityScore;
    CurrentEmotionalState = BehaviorData.EmotionalState;
    
    // Broadcast to Blueprint
    OnPlayerBehaviorDetected.Broadcast(PlayerID, BehaviorData);
}

void UHarmonyEnginePlayerComponent::OnInterventionTriggeredInternal(const FString& DetectedPlayerID, const FHarmonyInterventionData& InterventionData)
{
    if (DetectedPlayerID != PlayerID)
    {
        return; // Not for this player
    }
    
    bIsUnderIntervention = true;
    
    // Show intervention to client
    if (IsLocalPlayerController())
    {
        ClientShowIntervention(InterventionData.InterventionMessage, InterventionData.InterventionType);
    }
    
    // Broadcast to Blueprint
    OnPlayerInterventionTriggered.Broadcast(PlayerID, InterventionData);
}

void UHarmonyEnginePlayerComponent::OnKindnessRewardInternal(const FString& DetectedPlayerID, const FKindnessReward& Reward)
{
    if (DetectedPlayerID != PlayerID)
    {
        return; // Not for this player
    }
    
    TotalKindnessPoints += Reward.KindnessPoints;
    
    // Show reward notification to client
    if (IsLocalPlayerController())
    {
        ClientShowRewardNotification(Reward);
    }
    
    // Broadcast to Blueprint
    OnPlayerKindnessReward.Broadcast(PlayerID, Reward);
}

// Utility function implementations

APlayerController* UHarmonyEnginePlayerComponent::GetOwningPlayerController() const
{
    if (AActor* Owner = GetOwner())
    {
        // Try direct cast first (if attached to PlayerController)
        if (APlayerController* PC = Cast<APlayerController>(Owner))
        {
            return PC;
        }
        
        // Try getting from PlayerState
        if (APlayerState* PS = Cast<APlayerState>(Owner))
        {
            return PS->GetPlayerController();
        }
        
        // Try getting from Pawn
        if (APawn* Pawn = Cast<APawn>(Owner))
        {
            return Cast<APlayerController>(Pawn->GetController());
        }
    }
    
    return nullptr;
}

bool UHarmonyEnginePlayerComponent::IsLocalPlayerController() const
{
    APlayerController* PC = GetOwningPlayerController();
    return PC && PC->IsLocalPlayerController();
}

void UHarmonyEnginePlayerComponent::ValidateConfiguration()
{
    if (BehaviorUpdateInterval <= 0.0f)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Invalid BehaviorUpdateInterval, using default"));
        BehaviorUpdateInterval = 30.0f;
    }
    
    if (!bEnableAutomaticReporting && !bEnableEmotionalTracking)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Both automatic reporting and emotional tracking are disabled"));
    }
}

void UHarmonyEnginePlayerComponent::CleanupOldData()
{
    FDateTime CurrentTime = FDateTime::Now();
    
    // Remove actions older than 10 minutes
    RecentActions.RemoveAll([CurrentTime](const FString& Action) {
        // In a full implementation, this would parse timestamps from action strings
        return false; // Simplified for now
    });
    
    // Remove messages older than 5 minutes
    if (RecentChatMessages.Num() > 15)
    {
        RecentChatMessages.RemoveAt(0, 5);
    }
}

int32 UHarmonyEnginePlayerComponent::CountPositiveActions() const
{
    int32 PositiveCount = 0;
    for (const FString& Action : RecentActions)
    {
        if (Action.Contains(TEXT("Pos")))
        {
            PositiveCount++;
        }
    }
    return PositiveCount;
}

int32 UHarmonyEnginePlayerComponent::CountNegativeActions() const
{
    int32 NegativeCount = 0;
    for (const FString& Action : RecentActions)
    {
        if (Action.Contains(TEXT("Neg")))
        {
            NegativeCount++;
        }
    }
    return NegativeCount;
}

float UHarmonyEnginePlayerComponent::CalculateToxicityFromActions() const
{
    int32 TotalActions = RecentActions.Num();
    if (TotalActions == 0)
    {
        return 0.0f;
    }
    
    int32 NegativeActions = CountNegativeActions();
    return static_cast<float>(NegativeActions) / TotalActions;
}

float UHarmonyEnginePlayerComponent::CalculatePositivityFromActions() const
{
    int32 TotalActions = RecentActions.Num();
    if (TotalActions == 0)
    {
        return 0.0f;
    }
    
    int32 PositiveActions = CountPositiveActions();
    return static_cast<float>(PositiveActions) / TotalActions;
}

// Implementation of missing server processing functions

void UHarmonyEnginePlayerComponent::ProcessPlayerActionOnServer(const FString& ActionType, bool bIsPositive, float Intensity)
{
    if (!HarmonyEngineSubsystem || PlayerID.IsEmpty())
    {
        return;
    }

    // Create behavior snapshot for this action
    FPlayerBehaviorSnapshot BehaviorData;
    BehaviorData.PlayerID = PlayerID;
    BehaviorData.Timestamp = FDateTime::Now();

    // Update action counts
    if (bIsPositive)
    {
        BehaviorData.PositiveActionsCount = 1;
        BehaviorData.PositivityScore = Intensity;

        // Award kindness points for positive actions
        int32 KindnessPoints = FMath::RoundToInt(Intensity * 10.0f);
        HarmonyEngineSubsystem->AwardKindnessPoints(PlayerID, KindnessPoints,
            FString::Printf(TEXT("Positive action: %s"), *ActionType));
    }
    else
    {
        BehaviorData.NegativeActionsCount = 1;
        BehaviorData.ToxicityScore = Intensity;

        // Increase frustration for negative actions
        BehaviorData.FrustrationLevel = Intensity * 0.5f;
    }

    // Update Harmony Engine
    HarmonyEngineSubsystem->UpdatePlayerBehavior(PlayerID, BehaviorData);

    // Update client with new data
    if (APlayerController* PC = GetOwningPlayerController())
    {
        ClientUpdateHarmonyData(BehaviorData.ToxicityScore, BehaviorData.PositivityScore,
            BehaviorData.EmotionalState, TotalKindnessPoints);
    }
}

void UHarmonyEnginePlayerComponent::ProcessChatMessageOnServer(const FString& Message)
{
    if (!HarmonyEngineSubsystem || PlayerID.IsEmpty())
    {
        return;
    }

    // Analyze message for toxicity and positivity
    float ToxicityScore = AnalyzeMessageToxicity(Message);
    float PositivityScore = AnalyzeMessagePositivity(Message);

    // Create behavior snapshot
    FPlayerBehaviorSnapshot BehaviorData;
    BehaviorData.PlayerID = PlayerID;
    BehaviorData.Timestamp = FDateTime::Now();
    BehaviorData.ToxicityScore = ToxicityScore;
    BehaviorData.PositivityScore = PositivityScore;

    // Update action counts based on message analysis
    if (ToxicityScore > 0.5f)
    {
        BehaviorData.NegativeActionsCount = 1;
        BehaviorData.FrustrationLevel = ToxicityScore;
    }
    else if (PositivityScore > 0.5f)
    {
        BehaviorData.PositiveActionsCount = 1;

        // Award kindness points for positive messages
        int32 KindnessPoints = FMath::RoundToInt(PositivityScore * 5.0f);
        HarmonyEngineSubsystem->AwardKindnessPoints(PlayerID, KindnessPoints, TEXT("Positive communication"));
    }

    // Update Harmony Engine
    HarmonyEngineSubsystem->UpdatePlayerBehavior(PlayerID, BehaviorData);

    // Update client
    if (APlayerController* PC = GetOwningPlayerController())
    {
        ClientUpdateHarmonyData(ToxicityScore, PositivityScore, CurrentEmotionalState, TotalKindnessPoints);
    }
}

void UHarmonyEnginePlayerComponent::ProcessEmotionalStateOnServer(EEmotionalState NewEmotionalState)
{
    if (!HarmonyEngineSubsystem || PlayerID.IsEmpty())
    {
        return;
    }

    CurrentEmotionalState = NewEmotionalState;

    // Create behavior snapshot with emotional state
    FPlayerBehaviorSnapshot BehaviorData;
    BehaviorData.PlayerID = PlayerID;
    BehaviorData.Timestamp = FDateTime::Now();
    BehaviorData.EmotionalState = NewEmotionalState;

    // Adjust scores based on emotional state
    switch (NewEmotionalState)
    {
        case EEmotionalState::Happy:
        case EEmotionalState::Excited:
            BehaviorData.PositivityScore = 0.8f;
            BehaviorData.FrustrationLevel = 0.1f;
            break;
        case EEmotionalState::Angry:
        case EEmotionalState::Frustrated:
            BehaviorData.ToxicityScore = 0.6f;
            BehaviorData.FrustrationLevel = 0.8f;
            break;
        case EEmotionalState::Sad:
        case EEmotionalState::Anxious:
            BehaviorData.FrustrationLevel = 0.5f;
            break;
        default:
            // Neutral state
            break;
    }

    // Update Harmony Engine
    HarmonyEngineSubsystem->UpdatePlayerBehavior(PlayerID, BehaviorData);

    // Update client
    if (APlayerController* PC = GetOwningPlayerController())
    {
        ClientUpdateHarmonyData(BehaviorData.ToxicityScore, BehaviorData.PositivityScore,
            NewEmotionalState, TotalKindnessPoints);
    }
}

void UHarmonyEnginePlayerComponent::AnalyzeRecentBehavior()
{
    if (!bEnableEmotionalTracking || PlayerID.IsEmpty())
    {
        return;
    }

    // Analyze recent actions and messages to determine emotional state
    int32 PositiveActions = CountPositiveActions();
    int32 NegativeActions = CountNegativeActions();
    int32 TotalActions = RecentActions.Num();

    if (TotalActions == 0)
    {
        return; // No recent activity to analyze
    }

    // Calculate emotional state based on action patterns
    float PositiveRatio = static_cast<float>(PositiveActions) / TotalActions;
    float NegativeRatio = static_cast<float>(NegativeActions) / TotalActions;

    EEmotionalState PredictedState = CurrentEmotionalState;

    if (PositiveRatio > 0.7f)
    {
        PredictedState = EEmotionalState::Happy;
    }
    else if (NegativeRatio > 0.6f)
    {
        PredictedState = EEmotionalState::Frustrated;
    }
    else if (NegativeRatio > 0.8f)
    {
        PredictedState = EEmotionalState::Angry;
    }
    else if (PositiveRatio > 0.4f)
    {
        PredictedState = EEmotionalState::Calm;
    }

    // Update emotional state if it has changed
    if (PredictedState != CurrentEmotionalState)
    {
        ReportEmotionalState(PredictedState);
    }
}

void UHarmonyEnginePlayerComponent::ProcessActionHistory()
{
    // Process and clean up action history
    FDateTime CurrentTime = FDateTime::Now();

    // Remove actions older than 10 minutes for analysis
    RecentActions.RemoveAll([CurrentTime](const FString& Action) {
        // In a full implementation, this would parse timestamps from action strings
        // For now, we'll just limit the array size
        return false;
    });

    // Limit array size to prevent memory issues
    if (RecentActions.Num() > 100)
    {
        RecentActions.RemoveAt(0, 20); // Remove oldest 20 actions
    }
}

float UHarmonyEnginePlayerComponent::AnalyzeMessageToxicity(const FString& Message)
{
    float ToxicityScore = 0.0f;
    FString LowerMessage = Message.ToLower();

    // Simple keyword-based toxicity detection
    TArray<FString> ToxicKeywords = {
        TEXT("noob"), TEXT("trash"), TEXT("garbage"), TEXT("idiot"), TEXT("stupid"),
        TEXT("hate"), TEXT("kill yourself"), TEXT("uninstall"), TEXT("ez"), TEXT("rekt"),
        TEXT("toxic"), TEXT("cancer"), TEXT("kys"), TEXT("die"), TEXT("loser")
    };

    for (const FString& Keyword : ToxicKeywords)
    {
        if (LowerMessage.Contains(Keyword))
        {
            ToxicityScore += 0.2f;
        }
    }

    // Check for excessive caps (shouting)
    int32 CapsCount = 0;
    for (TCHAR Char : Message)
    {
        if (FChar::IsUpper(Char))
        {
            CapsCount++;
        }
    }

    if (Message.Len() > 0)
    {
        float CapsRatio = static_cast<float>(CapsCount) / Message.Len();
        if (CapsRatio > 0.7f && Message.Len() > 5)
        {
            ToxicityScore += 0.3f;
        }
    }

    return FMath::Clamp(ToxicityScore, 0.0f, 1.0f);
}

float UHarmonyEnginePlayerComponent::AnalyzeMessagePositivity(const FString& Message)
{
    float PositivityScore = 0.0f;
    FString LowerMessage = Message.ToLower();

    // Positive keywords detection
    TArray<FString> PositiveKeywords = {
        TEXT("good job"), TEXT("well done"), TEXT("nice"), TEXT("awesome"), TEXT("great"),
        TEXT("thanks"), TEXT("thank you"), TEXT("please"), TEXT("sorry"), TEXT("good luck"),
        TEXT("gg"), TEXT("wp"), TEXT("well played"), TEXT("amazing"), TEXT("fantastic"),
        TEXT("excellent"), TEXT("brilliant"), TEXT("perfect"), TEXT("love"), TEXT("respect")
    };

    for (const FString& Keyword : PositiveKeywords)
    {
        if (LowerMessage.Contains(Keyword))
        {
            PositivityScore += 0.2f;
        }
    }

    // Encouraging phrases
    TArray<FString> EncouragingPhrases = {
        TEXT("you can do it"), TEXT("don't give up"), TEXT("keep trying"), TEXT("we got this"),
        TEXT("team work"), TEXT("let's go"), TEXT("nice try"), TEXT("almost had it"),
        TEXT("good effort"), TEXT("keep it up"), TEXT("you're improving")
    };

    for (const FString& Phrase : EncouragingPhrases)
    {
        if (LowerMessage.Contains(Phrase))
        {
            PositivityScore += 0.3f;
        }
    }

    return FMath::Clamp(PositivityScore, 0.0f, 1.0f);
}
