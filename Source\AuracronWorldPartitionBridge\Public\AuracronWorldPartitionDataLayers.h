// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Data Layers Header
// Bridge 3.4: World Partition - Data Layers

#pragma once

#include "CoreMinimal.h"
#include "AuracronWorldPartitionBridgeAPI.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"

// Data Layer includes for UE5.6
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/DataLayer/ActorDataLayer.h"

// Engine includes
#include "Engine/World.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"

#include "AuracronWorldPartitionDataLayers.generated.h"

// Forward declarations
class UAuracronDataLayerManager;
class UDataLayerSubsystem;
class UDataLayerAsset;
class UDataLayerInstance;

// =============================================================================
// DATA LAYER TYPES AND ENUMS
// =============================================================================

// Data layer states
UENUM(BlueprintType)
enum class EAuracronDataLayerState : uint8
{
    Unloaded                UMETA(DisplayName = "Unloaded"),
    Loading                 UMETA(DisplayName = "Loading"),
    Loaded                  UMETA(DisplayName = "Loaded"),
    Unloading               UMETA(DisplayName = "Unloading"),
    Failed                  UMETA(DisplayName = "Failed")
};

// Data layer types
UENUM(BlueprintType)
enum class EAuracronDataLayerType : uint8
{
    Runtime                 UMETA(DisplayName = "Runtime"),
    Editor                  UMETA(DisplayName = "Editor"),
    Streaming               UMETA(DisplayName = "Streaming"),
    Static                  UMETA(DisplayName = "Static"),
    Dynamic                 UMETA(DisplayName = "Dynamic")
};

// Data layer visibility
UENUM(BlueprintType)
enum class EAuracronDataLayerVisibility : uint8
{
    Hidden                  UMETA(DisplayName = "Hidden"),
    Visible                 UMETA(DisplayName = "Visible"),
    PartiallyVisible        UMETA(DisplayName = "Partially Visible"),
    Transitioning           UMETA(DisplayName = "Transitioning")
};

// Data layer priority
UENUM(BlueprintType)
enum class EAuracronDataLayerPriority : uint8
{
    Lowest                  UMETA(DisplayName = "Lowest"),
    Low                     UMETA(DisplayName = "Low"),
    Normal                  UMETA(DisplayName = "Normal"),
    High                    UMETA(DisplayName = "High"),
    Highest                 UMETA(DisplayName = "Highest"),
    Critical                UMETA(DisplayName = "Critical")
};

// =============================================================================
// DATA LAYER CONFIGURATION
// =============================================================================

/**
 * Data Layer Configuration
 * Configuration settings for data layer management
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronDataLayerConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Layers")
    bool bEnableDataLayers = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Layers")
    bool bEnableRuntimeSwitching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Layers")
    bool bEnableConditionalLoading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxConcurrentLayerOperations = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LayerTransitionTime = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableLayerBlending = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float MaxLayerMemoryUsageMB = 1024.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableLayerCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableLayerDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogLayerOperations = false;

    FAuracronDataLayerConfiguration()
    {
        bEnableDataLayers = true;
        bEnableRuntimeSwitching = true;
        bEnableConditionalLoading = true;
        MaxConcurrentLayerOperations = 4;
        LayerTransitionTime = 1.0f;
        bEnableLayerBlending = true;
        MaxLayerMemoryUsageMB = 1024.0f;
        bEnableLayerCaching = true;
        bEnableLayerDebug = false;
        bLogLayerOperations = false;
    }
};

// =============================================================================
// DATA LAYER INFO
// =============================================================================

/**
 * Data Layer Info
 * Information about a data layer
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronDataLayerInfo
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    FString LayerId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    FString LayerName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    FString LayerLabel;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    EAuracronDataLayerType LayerType = EAuracronDataLayerType::Runtime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    EAuracronDataLayerState State = EAuracronDataLayerState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    EAuracronDataLayerVisibility Visibility = EAuracronDataLayerVisibility::Hidden;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    EAuracronDataLayerPriority Priority = EAuracronDataLayerPriority::Normal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    bool bIsRuntimeToggleable = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    bool bIsInitiallyLoaded = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    int32 ActorCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    TArray<FString> Dependencies;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    TArray<FString> Tags;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Layer Info")
    FDateTime LastModifiedTime;

    FAuracronDataLayerInfo()
    {
        LayerType = EAuracronDataLayerType::Runtime;
        State = EAuracronDataLayerState::Unloaded;
        Visibility = EAuracronDataLayerVisibility::Hidden;
        Priority = EAuracronDataLayerPriority::Normal;
        bIsRuntimeToggleable = true;
        bIsInitiallyLoaded = false;
        MemoryUsageMB = 0.0f;
        ActorCount = 0;
        CreationTime = FDateTime::Now();
        LastModifiedTime = CreationTime;
    }
};

// =============================================================================
// DATA LAYER CONDITION
// =============================================================================

/**
 * Data Layer Condition
 * Condition for conditional layer loading
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronDataLayerCondition
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Condition")
    FString ConditionName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Condition")
    FString ConditionExpression;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Condition")
    TMap<FString, FString> Parameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Condition")
    bool bIsActive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Condition")
    float EvaluationInterval = 1.0f;

    FAuracronDataLayerCondition()
    {
        bIsActive = true;
        EvaluationInterval = 1.0f;
    }

    // Evaluate condition
    bool EvaluateCondition() const;

    // Helper methods for condition evaluation
    bool EvaluateConditionExpression(const FString& Expression, const FAuracronDataLayerContext& Context) const;
    bool EvaluateComparisonExpression(const FString& Expression, const FString& Operator, const FAuracronDataLayerContext& Context) const;
    bool EvaluateFunctionCall(const FString& Expression, const FAuracronDataLayerContext& Context) const;
    bool EvaluateBooleanVariable(const FString& Expression, const FAuracronDataLayerContext& Context) const;
    float GetVariableValue(const FString& Variable, const FAuracronDataLayerContext& Context) const;
};

// =============================================================================
// DATA LAYER STATISTICS
// =============================================================================

/**
 * Data Layer Statistics
 * Performance and usage statistics for data layers
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronDataLayerStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalLayers = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LoadedLayers = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 VisibleLayers = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float TotalMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageLoadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LayerSwitches = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 FailedOperations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float LayerEfficiency = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronDataLayerStatistics()
    {
        TotalLayers = 0;
        LoadedLayers = 0;
        VisibleLayers = 0;
        TotalMemoryUsageMB = 0.0f;
        AverageLoadingTime = 0.0f;
        LayerSwitches = 0;
        FailedOperations = 0;
        LayerEfficiency = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// DATA LAYER MANAGER
// =============================================================================

/**
 * Data Layer Manager
 * Central manager for data layer operations
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONWORLDPARTITIONBRIDGE_API UAuracronDataLayerManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    static UAuracronDataLayerManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    void Initialize(const FAuracronDataLayerConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    void Tick(float DeltaTime);

    // Layer creation and management
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    FString CreateDataLayer(const FString& LayerName, EAuracronDataLayerType LayerType = EAuracronDataLayerType::Runtime);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool RemoveDataLayer(const FString& LayerId);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    FAuracronDataLayerInfo GetDataLayerInfo(const FString& LayerId) const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    TArray<FAuracronDataLayerInfo> GetAllDataLayers() const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    TArray<FString> GetDataLayerNames() const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool DoesDataLayerExist(const FString& LayerId) const;

    // Layer state management
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool SetDataLayerState(const FString& LayerId, EAuracronDataLayerState NewState);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    EAuracronDataLayerState GetDataLayerState(const FString& LayerId) const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool LoadDataLayer(const FString& LayerId);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool UnloadDataLayer(const FString& LayerId);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool ToggleDataLayer(const FString& LayerId);

    // Layer visibility management
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool SetDataLayerVisibility(const FString& LayerId, EAuracronDataLayerVisibility Visibility);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    EAuracronDataLayerVisibility GetDataLayerVisibility(const FString& LayerId) const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool ShowDataLayer(const FString& LayerId);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool HideDataLayer(const FString& LayerId);

    // Runtime switching
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool SwitchToDataLayerSet(const TArray<FString>& LayerIds);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool BlendDataLayers(const TArray<FString>& LayerIds, const TArray<float>& BlendWeights);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool TransitionToDataLayer(const FString& FromLayerId, const FString& ToLayerId, float TransitionTime = 1.0f);

    // Conditional loading
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool AddLayerCondition(const FString& LayerId, const FAuracronDataLayerCondition& Condition);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool RemoveLayerCondition(const FString& LayerId, const FString& ConditionName);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    TArray<FAuracronDataLayerCondition> GetLayerConditions(const FString& LayerId) const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    void EvaluateAllConditions();

    // Layer dependencies
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool AddLayerDependency(const FString& LayerId, const FString& DependencyLayerId);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool RemoveLayerDependency(const FString& LayerId, const FString& DependencyLayerId);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    TArray<FString> GetLayerDependencies(const FString& LayerId) const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool ValidateLayerDependencies(const FString& LayerId) const;

    // Layer queries
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    TArray<FString> GetLoadedLayers() const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    TArray<FString> GetVisibleLayers() const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    TArray<FString> GetLayersByType(EAuracronDataLayerType LayerType) const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    TArray<FString> GetLayersByTag(const FString& Tag) const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    void SetConfiguration(const FAuracronDataLayerConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    FAuracronDataLayerConfiguration GetConfiguration() const;

    // Statistics and monitoring
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    FAuracronDataLayerStatistics GetDataLayerStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    float GetTotalMemoryUsage() const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    int32 GetLoadedLayerCount() const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    int32 GetTotalLayerCount() const;

    // Debug and utilities
    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    void EnableLayerDebug(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    bool IsLayerDebugEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    void LogLayerState() const;

    UFUNCTION(BlueprintCallable, Category = "Data Layer Manager")
    void DrawDebugLayerInfo(UWorld* World) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDataLayerStateChanged, FString, LayerId, EAuracronDataLayerState, NewState);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDataLayerVisibilityChanged, FString, LayerId, EAuracronDataLayerVisibility, NewVisibility);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnDataLayerTransitionStarted, FString, FromLayerId, FString, ToLayerId, float, TransitionTime);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDataLayerTransitionCompleted, FString, FromLayerId, FString, ToLayerId);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDataLayerStateChanged OnDataLayerStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDataLayerVisibilityChanged OnDataLayerVisibilityChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDataLayerTransitionStarted OnDataLayerTransitionStarted;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDataLayerTransitionCompleted OnDataLayerTransitionCompleted;

private:
    static UAuracronDataLayerManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronDataLayerConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    UPROPERTY()
    TWeakObjectPtr<UDataLayerSubsystem> DataLayerSubsystem;

    // Cached world reference
    TWeakObjectPtr<UWorld> CachedWorld;

    // Layer data
    TMap<FString, FAuracronDataLayerInfo> DataLayers;
    TMap<FString, TArray<FAuracronDataLayerCondition>> LayerConditions;
    TSet<FString> LoadedLayers;
    TSet<FString> VisibleLayers;

    // Statistics
    FAuracronDataLayerStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection LayerLock;

    // Timing
    float LastConditionEvaluationTime = 0.0f;

    // Internal functions
    void UpdateStatistics();
    FString GenerateLayerId(const FString& LayerName) const;
    bool ValidateLayerId(const FString& LayerId) const;
    void OnLayerStateChangedInternal(const FString& LayerId, EAuracronDataLayerState NewState);
    void OnLayerVisibilityChangedInternal(const FString& LayerId, EAuracronDataLayerVisibility NewVisibility);
    void ValidateConfiguration();
    UDataLayerSubsystem* GetDataLayerSubsystem() const;
    bool LoadLayerDependencies(const FString& LayerId);
    bool UnloadLayerDependents(const FString& LayerId);
};
