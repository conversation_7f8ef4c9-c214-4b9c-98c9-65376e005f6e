// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON EOS Bridge - Main Module Implementation
// Bridge 2.1: EOS Integration - Core Infrastructure

#include "AuracronEOSBridge.h"
#include "Online/OnlineSessionNames.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Async/Async.h"
#include "Kismet/GameplayStatics.h"

// Modern UE5.6 Online Subsystem includes
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "Interfaces/OnlineIdentityInterface.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "Interfaces/OnlineFriendsInterface.h"
#include "Interfaces/OnlineAchievementsInterface.h"
#include "OnlineSessionSettings.h"

UAuracronEOSBridge::UAuracronEOSBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f;
    
    // Initialize connection status
    CurrentConnectionStatus = EAuracronEOSConnectionStatus::Disconnected;
    bIsInSession = false;
    
    // Initialize configuration with defaults
    Configuration.bAutoLogin = true;
    Configuration.bEnableFriends = true;
    Configuration.bEnableAchievements = true;
    Configuration.bEnableStats = true;
    Configuration.MaxRetryAttempts = 3;
    Configuration.RetryDelaySeconds = 5.0f;
    
    // Initialize retry counter
    CurrentRetryAttempts = 0;
}

void UAuracronEOSBridge::BeginPlay()
{
    Super::BeginPlay();
    
    // Initialize EOS subsystem
    OnlineSubsystem = IOnlineSubsystem::Get();
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to get Online Subsystem"));
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        return;
    }
    
    // Get interfaces
    IdentityInterface = OnlineSubsystem->GetIdentityInterface();
    SessionInterface = OnlineSubsystem->GetSessionInterface();
    FriendsInterface = OnlineSubsystem->GetFriendsInterface();
    AchievementsInterface = OnlineSubsystem->GetAchievementsInterface();

    // Initialize session search
    SessionSearch = MakeShareable(new FOnlineSessionSearch());
    
    if (!IdentityInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to get Identity Interface"));
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        return;
    }
    
    // Setup delegates using modern UE5.6 API
    if (IdentityInterface.IsValid())
    {
        IdentityInterface->OnLoginCompleteDelegates->AddUObject(this, &UAuracronEOSBridge::OnLoginComplete);
    }
    
    if (SessionInterface.IsValid())
    {
        SessionInterface->OnCreateSessionCompleteDelegates.AddUObject(this, &UAuracronEOSBridge::OnCreateSessionComplete);
    }
    
    // Auto-login if enabled
    if (Configuration.bAutoLogin)
    {
        LoginWithEOS(TEXT("AccountPortal"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: EOS Bridge initialized successfully"));
}

void UAuracronEOSBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Cleanup delegates
    if (IdentityInterface.IsValid())
    {
        IdentityInterface->OnLoginCompleteDelegates->RemoveAll(this);
    }
    
    if (SessionInterface.IsValid())
    {
        SessionInterface->OnCreateSessionCompleteDelegates.RemoveAll(this);
    }
    
    // Logout if connected
    if (CurrentConnectionStatus == EAuracronEOSConnectionStatus::Authenticated)
    {
        LogoutFromEOS();
    }
    
    Super::EndPlay(EndPlayReason);
}

void UAuracronEOSBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    // Update connection status and handle reconnection logic
    UpdateConnectionStatus();
}

bool UAuracronEOSBridge::LoginWithEOS(const FString& LoginType)
{
    if (!IdentityInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Identity Interface not available"));
        return false;
    }
    
    if (CurrentConnectionStatus == EAuracronEOSConnectionStatus::Authenticated)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Already authenticated"));
        return true;
    }
    
    CurrentConnectionStatus = EAuracronEOSConnectionStatus::Connecting;
    
    // Use modern UE5.6 login credentials
    FOnlineAccountCredentials Credentials;
    Credentials.Type = TEXT("accountportal"); // Modern EOS login type
    Credentials.Id = TEXT("");
    Credentials.Token = TEXT("");
    
    bool bLoginStarted = IdentityInterface->Login(0, Credentials);
    if (!bLoginStarted)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to start login process"));
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Login process started"));
    return true;
}

bool UAuracronEOSBridge::LogoutFromEOS()
{
    if (!IdentityInterface.IsValid())
    {
        return false;
    }

    if (CurrentConnectionStatus != EAuracronEOSConnectionStatus::Authenticated)
    {
        return false;
    }
    
    IdentityInterface->Logout(0);
    CurrentConnectionStatus = EAuracronEOSConnectionStatus::Disconnected;
    CurrentUserID.Empty();
    CurrentDisplayName.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Logged out from EOS"));
    return true;
}

bool UAuracronEOSBridge::CreateSession(const FAuracronSessionConfiguration& SessionConfig)
{
    if (!SessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Session Interface not available"));
        return false;
    }
    
    if (CurrentConnectionStatus != EAuracronEOSConnectionStatus::Authenticated)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Not authenticated"));
        return false;
    }
    
    // Create session settings using modern UE5.6 API
    TSharedPtr<FOnlineSessionSettings> SessionSettings = MakeShareable(new FOnlineSessionSettings());
    SessionSettings->NumPublicConnections = SessionConfig.MaxPlayers;
    SessionSettings->NumPrivateConnections = 0;
    SessionSettings->bIsLANMatch = false;
    SessionSettings->bShouldAdvertise = SessionConfig.bIsPublic;
    SessionSettings->bAllowJoinInProgress = SessionConfig.bAllowJoinInProgress;
    SessionSettings->bAllowInvites = true;
    SessionSettings->bUsesPresence = true;
    SessionSettings->bUseLobbiesIfAvailable = true;
    
    // Set custom session data using modern UE5.6 API
    SessionSettings->Set(FName("GameMode"), SessionConfig.GameMode, EOnlineDataAdvertisementType::ViaOnlineService);
    SessionSettings->Set(FName("MapName"), SessionConfig.MapName, EOnlineDataAdvertisementType::ViaOnlineService);
    
    // Get user ID for session creation
    TSharedPtr<const FUniqueNetId> UserId = IdentityInterface->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to get User ID"));
        return false;
    }
    
    bool bSessionCreated = SessionInterface->CreateSession(*UserId, FName(*SessionConfig.SessionName), *SessionSettings);
    if (!bSessionCreated)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create session"));
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Session creation started"));
    return true;
}

void UAuracronEOSBridge::OnLoginComplete(int32 LocalUserNum, bool bWasSuccessful, const FUniqueNetId& UserId, const FString& Error)
{
    if (bWasSuccessful)
    {
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Authenticated;
        CurrentUserID = UserId.ToString();
        
        // Get display name using modern API
        if (IdentityInterface.IsValid())
        {
            CurrentDisplayName = IdentityInterface->GetPlayerNickname(0);
        }
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Login EOS bem-sucedido - Usuário: %s"), *CurrentDisplayName);
        
        // Load friends list if enabled
        if (Configuration.bEnableFriends)
        {
            LoadFriendsList();
        }
    }
    else
    {
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Login EOS falhou: %s"), *Error);
        
        // Handle retry logic
        HandleRetryLogic();
    }
    
    // Broadcast login completion
    OnEOSLoginCompleted.Broadcast(bWasSuccessful, Error);
}

void UAuracronEOSBridge::OnCreateSessionComplete(FName SessionName, bool bWasSuccessful)
{
    if (bWasSuccessful)
    {
        bIsInSession = true;
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Session created successfully: %s"), *SessionName.ToString());
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create session: %s"), *SessionName.ToString());
    }
    
    // Broadcast session creation result
    OnSessionCreated.Broadcast(bWasSuccessful, SessionName.ToString());
}

bool UAuracronEOSBridge::LoadFriendsList()
{
    if (!FriendsInterface.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Friends Interface not available"));
        return false;
    }
    
    // Read friends list using modern UE5.6 API
    FriendsInterface->ReadFriendsList(0, EFriendsLists::ToString(EFriendsLists::Default),
        FOnReadFriendsListComplete::CreateUObject(this, &UAuracronEOSBridge::OnReadFriendsListComplete));
    return true;
}

void UAuracronEOSBridge::OnReadFriendsListComplete(int32 LocalUserNum, bool bWasSuccessful, const FString& ListName, const FString& ErrorStr)
{
    if (bWasSuccessful)
    {
        if (FriendsInterface.IsValid())
        {
            // Clear existing friends list
            // Note: Using simple clear without mutex for now
            // In production, implement proper thread safety
            FriendsList.Empty();
            
            // Get friends list
            TArray<TSharedRef<FOnlineFriend>> Friends;
            FriendsInterface->GetFriendsList(0, ListName, Friends);
            
            // Convert to our format
            for (const auto& Friend : Friends)
            {
                FAuracronFriendData AuracronFriend;
                AuracronFriend.UserID = Friend->GetUserId()->ToString();
                AuracronFriend.DisplayName = Friend->GetDisplayName();
                AuracronFriend.bIsOnline = Friend->GetPresence().bIsOnline;
                AuracronFriend.StatusText = Friend->GetPresence().Status.StatusStr;
                
                // Add friend to list
                // Note: Using simple add without mutex for now
                // In production, implement proper thread safety
                FriendsList.Add(AuracronFriend);
            }
            
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Lista de amigos carregada - %d amigos"), FriendsList.Num());
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to load friends list: %s"), *ErrorStr);
    }
}

void UAuracronEOSBridge::UpdateConnectionStatus()
{
    // Implement connection status monitoring and auto-reconnection logic
    if (CurrentConnectionStatus == EAuracronEOSConnectionStatus::Error && Configuration.bAutoLogin)
    {
        // Implement retry logic with exponential backoff
        HandleRetryLogic();
    }
}

void UAuracronEOSBridge::HandleRetryLogic()
{
    if (CurrentRetryAttempts < Configuration.MaxRetryAttempts)
    {
        CurrentRetryAttempts++;
        float DelayTime = Configuration.RetryDelaySeconds * FMath::Pow(2.0f, CurrentRetryAttempts - 1);
        
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Retrying login in %f seconds (Attempt %d/%d)"), 
               DelayTime, CurrentRetryAttempts, Configuration.MaxRetryAttempts);
        
        // Schedule retry using lambda to handle parameter
        if (GetWorld())
        {
            FTimerDelegate TimerDelegate;
            TimerDelegate.BindLambda([this]()
            {
                LoginWithEOS(TEXT("AccountPortal"));
            });
            GetWorld()->GetTimerManager().SetTimer(RetryTimerHandle, TimerDelegate, DelayTime, false);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Max retry attempts reached. Login failed permanently."));
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
    }
}

void UAuracronEOSBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    DOREPLIFETIME(UAuracronEOSBridge, CurrentConnectionStatus);
    DOREPLIFETIME(UAuracronEOSBridge, bIsInSession);
    DOREPLIFETIME(UAuracronEOSBridge, CurrentUserID);
}

EAuracronEOSConnectionStatus UAuracronEOSBridge::GetAuthenticationStatus() const
{
    return CurrentConnectionStatus;
}

FString UAuracronEOSBridge::GetUserID() const
{
    return CurrentUserID;
}

bool UAuracronEOSBridge::FindSessions(EAuracronEOSSessionType SessionType, const FString& Region)
{
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for FindSessions"));
        return false;
    }
    
    IOnlineSessionPtr LocalSessionInterface = OnlineSubsystem->GetSessionInterface();
    if (!LocalSessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: SessionInterface not available"));
        return false;
    }
    
    // Create search settings
    SessionSearch = MakeShareable(new FOnlineSessionSearch());
    SessionSearch->bIsLanQuery = false;
    SessionSearch->MaxSearchResults = 50;
    SessionSearch->PingBucketSize = 50;
    
    // Set session type filter
    SessionSearch->QuerySettings.Set(SEARCH_KEYWORDS, FString::Printf(TEXT("AuracronSessionType_%s"), *UEnum::GetValueAsString(SessionType)), EOnlineComparisonOp::Equals);
    
    if (!Region.IsEmpty())
    {
        // UE 5.6: Use custom region setting instead of deprecated SEARCH_REGION
        SessionSearch->QuerySettings.Set(FName(TEXT("REGION")), Region, EOnlineComparisonOp::Equals);
    }
    
    // Start the search
    return LocalSessionInterface->FindSessions(0, SessionSearch.ToSharedRef());
}

bool UAuracronEOSBridge::JoinSession(const FString& SessionID)
{
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for JoinSession"));
        return false;
    }
    
    IOnlineSessionPtr LocalSessionInterface = OnlineSubsystem->GetSessionInterface();
    if (!LocalSessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: SessionInterface not available"));
        return false;
    }
    
    // Find the session in our search results
    if (SessionSearch.IsValid())
    {
        for (const FOnlineSessionSearchResult& SearchResult : SessionSearch->SearchResults)
        {
            FString ResultSessionId = SearchResult.GetSessionIdStr();
            if (ResultSessionId == SessionID)
            {
                return SessionInterface->JoinSession(0, NAME_GameSession, SearchResult);
            }
        }
    }
    
    UE_LOG(LogTemp, Error, TEXT("AURACRON: Session %s not found in search results"), *SessionID);
    return false;
}

bool UAuracronEOSBridge::LeaveSession()
{
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for LeaveSession"));
        return false;
    }
    
    IOnlineSessionPtr LocalSessionInterface = OnlineSubsystem->GetSessionInterface();
    if (!LocalSessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: SessionInterface not available"));
        return false;
    }
    
    if (!bIsInSession)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Not currently in a session"));
        return false;
    }
    
    return LocalSessionInterface->DestroySession(NAME_GameSession);
}

bool UAuracronEOSBridge::DestroySession()
{
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for DestroySession"));
        return false;
    }
    
    IOnlineSessionPtr LocalSessionInterface = OnlineSubsystem->GetSessionInterface();
    if (!LocalSessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: SessionInterface not available"));
        return false;
    }
    
    return LocalSessionInterface->DestroySession(NAME_GameSession);
}

// Implement other methods with modern UE5.6 APIs...
