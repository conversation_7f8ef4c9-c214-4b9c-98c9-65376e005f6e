#pragma once

#include "CoreMinimal.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Templates/UniquePtr.h"
#include "Containers/Array.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "AuracronBehaviorReader.generated.h"

// Forward declarations
class FAuracronDNAReader;

// MetaHuman behavior data structures
USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FMetaHumanBehaviorData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Behavior")
    float EmotionalIntensity = 0.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Behavior")
    FVector FacialExpressionVector = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, Category = "Behavior")
    float BodyLanguageScore = 0.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Behavior")
    TArray<FString> DetectedEmotions;
};

USTRUCT(BlueprintType)
struct AURACRONMETAHUMANBRIDGE_API FMetaHumanControlRigData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Control Rig")
    TMap<FString, float> ControlValues;

    UPROPERTY(BlueprintReadWrite, Category = "Control Rig")
    FTransform HeadTransform = FTransform::Identity;

    UPROPERTY(BlueprintReadWrite, Category = "Control Rig")
    TArray<FVector> BlendShapeWeights;
};

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronBehaviorReader, Log, All);

/**
 * Thread-safe wrapper for MetaHuman behavior data reading
 * Provides specialized access to behavior and control rig data
 */
class AURACRONMETAHUMANBRIDGE_API FAuracronBehaviorReader
{
public:
    FAuracronBehaviorReader();
    ~FAuracronBehaviorReader();

    // Core behavior reading operations
    bool InitializeFromReader(const FAuracronDNAReader& Reader);
    bool IsValid() const;
    void Reset();

    // Behavior data access
    FMetaHumanBehaviorData GetBehaviorData() const;
    FMetaHumanControlRigData GetControlRigData() const;

    // Control data access
    int32 GetControlCount() const;
    FString GetControlName(int32 ControlIndex) const;
    TArray<float> GetControlValues(int32 ControlIndex) const;
    TArray<float> GetControlMinValues(int32 ControlIndex) const;
    TArray<float> GetControlMaxValues(int32 ControlIndex) const;
    TArray<float> GetControlDefaultValues(int32 ControlIndex) const;

    // Animated map access
    int32 GetAnimatedMapCount() const;
    FString GetAnimatedMapName(int32 MapIndex) const;
    TArray<float> GetAnimatedMapValues(int32 MapIndex) const;
    TArray<int32> GetAnimatedMapIndices(int32 MapIndex) const;

    // Blend shape channel access
    int32 GetBlendShapeChannelCount() const;
    FString GetBlendShapeChannelName(int32 ChannelIndex) const;
    TArray<float> GetBlendShapeChannelValues(int32 ChannelIndex) const;
    TArray<int32> GetBlendShapeChannelIndices(int32 ChannelIndex) const;

    // Joint animation access
    int32 GetJointAnimationCount() const;
    FString GetJointAnimationName(int32 AnimationIndex) const;
    TArray<FVector> GetJointTranslationValues(int32 AnimationIndex) const;
    TArray<FRotator> GetJointRotationValues(int32 AnimationIndex) const;
    TArray<FVector> GetJointScaleValues(int32 AnimationIndex) const;

    // Machine learning behavior access
    int32 GetMLControlCount() const;
    FString GetMLControlName(int32 ControlIndex) const;
    TArray<float> GetMLControlValues(int32 ControlIndex) const;
    TArray<float> GetMLControlInputs(int32 ControlIndex) const;
    TArray<float> GetMLControlOutputs(int32 ControlIndex) const;

    // Neural network access
    int32 GetNeuralNetworkCount() const;
    FString GetNeuralNetworkName(int32 NetworkIndex) const;
    TArray<float> GetNeuralNetworkWeights(int32 NetworkIndex) const;
    TArray<float> GetNeuralNetworkBiases(int32 NetworkIndex) const;
    TArray<int32> GetNeuralNetworkTopology(int32 NetworkIndex) const;

    // Behavior validation
    bool ValidateBehaviorData() const;
    bool ValidateControlRigData() const;
    bool ValidateMLData() const;
    bool ValidateNeuralNetworkData() const;

    // Error handling
    FString GetLastError() const;
    TArray<FString> GetValidationWarnings() const;

    // Thread safety
    mutable FCriticalSection AccessMutex;

private:
    // Native behavior reader instance - UE 5.6 uses RigLogic instead of DNA API
#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    TUniquePtr<class URigLogic> NativeReader;
#else
    void* NativeReader;
#endif

    // State tracking
    FThreadSafeBool bIsValid;
    FString LastError;
    TArray<FString> ValidationWarnings;

    // Cached data for performance
    mutable bool bBehaviorDataCached;
    mutable FMetaHumanBehaviorData CachedBehaviorData;
    mutable bool bControlRigDataCached;
    mutable FMetaHumanControlRigData CachedControlRigData;

    // Internal helper methods
    bool ValidateControlIndex(int32 ControlIndex) const;
    bool ValidateAnimatedMapIndex(int32 MapIndex) const;
    bool ValidateBlendShapeChannelIndex(int32 ChannelIndex) const;
    bool ValidateJointAnimationIndex(int32 AnimationIndex) const;
    bool ValidateMLControlIndex(int32 ControlIndex) const;
    bool ValidateNeuralNetworkIndex(int32 NetworkIndex) const;

    void LogError(const FString& ErrorMessage) const;
    void LogWarning(const FString& WarningMessage) const;
    void ClearCache() const;

    // Prevent copying
    FAuracronBehaviorReader(const FAuracronBehaviorReader&) = delete;
    FAuracronBehaviorReader& operator=(const FAuracronBehaviorReader&) = delete;
};
